import {
  streamText,
  tool,
  UIMessage,
  convertToModelMessages,
  stepCountIs,
} from 'ai'
// import { openai } from '@ai-sdk/openai'
import { azure } from '@ai-sdk/azure'
import { z } from 'zod'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

// Define tools for demonstration
const tools = {
  getCurrentWeather: tool({
    description: 'Get the current weather for a location',
    inputSchema: z.object({
      location: z
        .string()
        .describe('The city and state, e.g. San Francisco, CA'),
      unit: z.enum(['celsius', 'fahrenheit']).optional().default('fahrenheit'),
    }),
    execute: async ({ location, unit }) => {
      // Simulate weather API call
      const temperature = Math.floor(Math.random() * 30) + 10
      const conditions = ['sunny', 'cloudy', 'rainy', 'partly cloudy'][
        Math.floor(Math.random() * 4)
      ]

      return {
        location,
        temperature: `${temperature}°${unit === 'celsius' ? 'C' : 'F'}`,
        conditions,
        humidity: `${Math.floor(Math.random() * 40) + 30}%`,
        windSpeed: `${Math.floor(Math.random() * 20) + 5} mph`,
      }
    },
  }),

  calculateMath: tool({
    description: 'Perform mathematical calculations',
    inputSchema: z.object({
      expression: z
        .string()
        .describe('The mathematical expression to evaluate'),
    }),
    execute: async ({ expression }) => {
      try {
        // Simple math evaluation (in production, use a proper math parser)
        const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '')
        const result = Function(`"use strict"; return (${sanitized})`)()

        return {
          expression,
          result: result.toString(),
          isValid: true,
        }
      } catch (error) {
        return {
          expression,
          result: 'Invalid expression',
          isValid: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      }
    },
  }),

  searchWeb: tool({
    description: 'Search the web for information',
    inputSchema: z.object({
      query: z.string().describe('The search query'),
      maxResults: z.number().optional().default(5),
    }),
    execute: async ({ query, maxResults }) => {
      // Simulate web search results
      const mockResults = [
        {
          title: `${query} - Wikipedia`,
          url: `https://en.wikipedia.org/wiki/${query.replace(/\s+/g, '_')}`,
          snippet: `Learn about ${query} on Wikipedia. Comprehensive information and references.`,
        },
        {
          title: `${query} News - Latest Updates`,
          url: `https://news.example.com/search?q=${encodeURIComponent(query)}`,
          snippet: `Latest news and updates about ${query}. Stay informed with recent developments.`,
        },
        {
          title: `${query} Guide - Complete Tutorial`,
          url: `https://guide.example.com/${query.toLowerCase().replace(/\s+/g, '-')}`,
          snippet: `Complete guide and tutorial about ${query}. Step-by-step instructions and tips.`,
        },
      ].slice(0, maxResults)

      return {
        query,
        results: mockResults,
        totalResults: mockResults.length,
      }
    },
  }),
}

export async function POST(req: Request) {
  try {
    const { messages }: { messages: UIMessage[] } = await req.json()

    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 })
    }

    const result = streamText({
      // Azure blocks reasoning summary, but if we use open ai api instead
      // This code will work and show reasoning tokens
      model: azure.responses('gpt-4.1-mini'),
      messages: convertToModelMessages(messages),
      tools,
      toolChoice: 'auto',
      maxOutputTokens: 2000,
      temperature: 0.7,
      // Enable multi-step tool calling - this is the key fix!
      stopWhen: stepCountIs(10),
      // Enable reasoning summaries for o1-series models
      providerOptions: {
        azure: {
          reasoningSummary: 'auto', // 'auto' for condensed or 'detailed' for comprehensive
        },
      },
      system: `You are a helpful AI assistant with advanced reasoning capabilities and access to tools for weather, calculations, and web search.

IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question. Do not end the conversation after just calling tools.

When using tools:
- Always explain what you're doing before calling a tool
- After tool execution, provide clear, helpful responses based on tool results
- If a tool fails, explain what went wrong and suggest alternatives
- Always conclude with a comprehensive answer to the user's original question

For math problems, use the calculateMath tool.
For weather information, use the getCurrentWeather tool.
For information searches, use the searchWeb tool.

You can think through complex problems step by step, and your reasoning process will be visible to users to help them understand your thought process.

Be conversational, helpful, and informative in your responses.`,
    })

    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('Chat API error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
