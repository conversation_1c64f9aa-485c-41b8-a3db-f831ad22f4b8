'use client'

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { FiMessageCircle } from 'react-icons/fi'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
} from '@/components/ai-elements/prompt-input'

export default function ChatDemoPage() {
  // Local input with explicit type annotation for clarity
  const [input, setInput] = useState<string>('')

  const { messages, sendMessage, status, error } = useChat({
    transport: new DefaultChatTransport({ api: '/api/chat-demo' }),
    experimental_throttle: 200,
    onError: error => {
      console.error('Chat error:', error)
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && status !== 'streaming') {
      sendMessage({ text: input })
      setInput('')
    }
  }

  return (
    <div className="flex h-screen flex-col bg-white">
      {/* Header - Compact style matching AI Pane */}
      <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FiMessageCircle className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              AI SDK Elements Chat Demo
            </h1>
            <p className="text-sm text-gray-600">
              ChatGPT-style interface with tool calling and reasoning support
            </p>
          </div>
        </div>
      </div>

      {/* Context Display - Matching AI Pane Chat style */}
      <div className="px-4 py-2 border-b border-gray-100 bg-white">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <span>Using web search and calculation tools</span>
          <span className="text-gray-400">Demo mode</span>
        </div>
      </div>

      {/* Chat Container */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Conversation className="flex-1">
          <ConversationContent>
            {messages.length === 0 && (
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <h2 className="text-lg font-medium text-gray-500">
                    Start a conversation
                  </h2>
                  <p className="text-sm text-gray-400">
                    Try asking about the weather, or request a calculation
                  </p>
                </div>
              </div>
            )}

            {messages.map(message => (
              <Message key={message.id} from={message.role}>
                <MessageContent>
                  {/* Reasoning will be handled in message parts */}

                  {/* Render message parts */}
                  {message.parts.map((part, index) => {
                    if (part.type === 'text') {
                      return (
                        <Response
                          key={`${message.id}-text-${index}`}
                          defaultOrigin="https://localhost:3000"
                        >
                          {part.text}
                        </Response>
                      )
                    }

                    if (part.type === 'reasoning') {
                      return (
                        <Reasoning
                          key={`${message.id}-reasoning-${index}`}
                          isStreaming={
                            status === 'streaming' &&
                            message.id === messages[messages.length - 1]?.id
                          }
                          defaultOpen={false}
                        >
                          <ReasoningTrigger />
                          <ReasoningContent>{part.text}</ReasoningContent>
                        </Reasoning>
                      )
                    }

                    // Handle tool UI parts if present
                    const maybeTool = part as any
                    if (
                      maybeTool &&
                      typeof maybeTool === 'object' &&
                      'state' in maybeTool &&
                      'type' in maybeTool &&
                      (('input' in maybeTool &&
                        maybeTool.input !== undefined) ||
                        ('output' in maybeTool &&
                          maybeTool.output !== undefined) ||
                        ('errorText' in maybeTool && maybeTool.errorText))
                    ) {
                      return (
                        <Tool key={`${message.id}-tool-${index}`}>
                          <ToolHeader
                            type={maybeTool.type}
                            state={maybeTool.state}
                          />
                          <ToolContent>
                            {'input' in maybeTool && (
                              <ToolInput input={maybeTool.input} />
                            )}
                            <ToolOutput
                              output={
                                'output' in maybeTool ? (
                                  typeof maybeTool.output === 'string' ? (
                                    maybeTool.output
                                  ) : (
                                    <pre className="text-xs">
                                      {JSON.stringify(
                                        maybeTool.output,
                                        null,
                                        2
                                      )}
                                    </pre>
                                  )
                                ) : undefined
                              }
                              errorText={
                                'errorText' in maybeTool
                                  ? maybeTool.errorText
                                  : undefined
                              }
                            />
                          </ToolContent>
                        </Tool>
                      )
                    }

                    return null
                  })}
                </MessageContent>
              </Message>
            ))}

            {error && (
              <div className="mx-4 rounded-lg border border-red-200 bg-red-50 p-4">
                <p className="text-sm text-red-600">Error: {error.message}</p>
              </div>
            )}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        {/* Input Area - Matching AI Pane Chat styling */}
        <div className="border-t border-gray-200 bg-white p-4">
          <div className="max-w-4xl mx-auto">
            <PromptInput
              onSubmit={handleSubmit}
              className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors"
            >
              <PromptInputTextarea
                value={input}
                onChange={e => setInput(e.target.value)}
                placeholder="Type your message here..."
                className="flex-1 min-h-[44px] max-h-32 resize-none border-0 bg-transparent px-4 py-3 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-0"
                disabled={status === 'streaming'}
              />
              <PromptInputSubmit
                status={status === 'streaming' ? 'streaming' : 'ready'}
                disabled={!input.trim() || status === 'streaming'}
                className="m-2 h-8 w-8 p-0 rounded-lg bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
              />
            </PromptInput>

            {/* Helpful hints - matching AI Pane Chat */}
            <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
              <span>Press Enter to send, Shift+Enter for new line</span>
              <span>{input.length} characters</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
