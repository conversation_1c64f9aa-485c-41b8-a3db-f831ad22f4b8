import NextAuth, { DefaultSession } from 'next-auth'
import Github<PERSON>rovider from 'next-auth/providers/github'
import GoogleProvider from 'next-auth/providers/google'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'

import prisma from '@/app/libs/prismadb'
import { UserStatus, SubscriptionTier } from '@prisma/client'

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      status: UserStatus
      subscription_tier: SubscriptionTier
      subscription: {
        tier: SubscriptionTier
        expiry: string | null
        cancelPending: boolean
      }
    } & DefaultSession['user']
  }
}

declare module 'next-auth' {
  interface JWT {
    id: string
    status: UserStatus
    subscription_tier: SubscriptionTier
    tierExpiry: string | null
    cancelPending: boolean
  }
}

const baseProviders = [
  GithubProvider({
    clientId: process.env.GITHUB_ID as string,
    clientSecret: process.env.GITHUB_SECRET as string,
  }),
  GoogleProvider({
    clientId: process.env.GOOGLE_CLIENT_ID as string,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
  }),
] as const

const allProviders: any[] = [...baseProviders]

// Add credentials provider only for non-production environments
if (process.env.NODE_ENV !== 'production') {
  allProviders.push(
    CredentialsProvider({
      name: 'E2E Test Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'text',
          placeholder: '<EMAIL>',
        },
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          throw new Error('Email is required for E2E login.')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email as string },
        })

        if (user) {
          // Return user object to create a session
          return user
        } else {
          // If you want to auto-create a user, you can do it here
          return null
        }
      },
    })
  )
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: allProviders,
  debug: process.env.NODE_ENV === 'development',
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  },
  pages: {
    signIn: '/',
    // After successful sign-in, default to screening unless a callbackUrl is specified.
    // Note: Client can still override via signIn(provider, { callbackUrl: '/screening' })
  },
  callbacks: {
    signIn: async ({ user }) => {
      // Check if user exists and is active
      const dbUser = await prisma.user.findUnique({
        where: { email: user.email as string },
        select: { status: true },
      })

      // Allow sign-in for new users or active existing users
      return !dbUser || dbUser.status === UserStatus.ACTIVE
    },

    jwt: async ({ token, user, trigger, session }) => {
      if (user) {
        // Fetch user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email as string },
          select: {
            id: true,
            status: true,
            subscription_tier: true,
            subscription_end_date: true,
            subscription_cancel_pending: true,
          },
        })

        // Add custom claims to the token
        if (dbUser) {
          token.id = dbUser.id
          token.status = dbUser.status
          token.subscription_tier = dbUser.subscription_tier
          token.tierExpiry = dbUser.subscription_end_date?.toISOString() || null
          token.cancelPending = dbUser.subscription_cancel_pending || false
        }
      }

      // Refresh subscription data on session update
      if (trigger === 'update' && token.id) {
        const dbUser = await prisma.user.findUnique({
          where: { id: token.id as string },
          select: {
            subscription_tier: true,
            subscription_end_date: true,
            subscription_cancel_pending: true,
          },
        })

        if (dbUser) {
          token.subscription_tier = dbUser.subscription_tier
          token.tierExpiry = dbUser.subscription_end_date?.toISOString() || null
          token.cancelPending = dbUser.subscription_cancel_pending || false
        }
      }

      // Handle manual session.update() calls – refresh subscription tier
      if (trigger === 'update') {
        if (
          session?.subscription_tier &&
          session.subscription_tier !== token.subscription_tier
        ) {
          token.subscription_tier =
            session.subscription_tier as SubscriptionTier
        } else if (token?.id) {
          // fetch latest tier if not provided
          try {
            const refreshedUser = await prisma.user.findUnique({
              where: { id: token.id as string },
              select: { subscription_tier: true },
            })
            if (refreshedUser?.subscription_tier) {
              token.subscription_tier = refreshedUser.subscription_tier
            }
          } catch (err) {
            console.error(
              'Failed to refresh subscription tier during JWT update',
              err
            )
          }
        }
      }

      return token
    },

    session: ({ session, token }) => {
      // Add custom session data
      if (session.user) {
        session.user.id = token.id as string
        session.user.status = token.status as UserStatus
        session.user.subscription_tier =
          token.subscription_tier as SubscriptionTier
        session.user.subscription = {
          tier: token.subscription_tier as SubscriptionTier,
          expiry: token.tierExpiry as string | null,
          cancelPending: token.cancelPending as boolean,
        }
      }
      return session
    },
  },
  secret: process.env.NEXTAUTH_SECRET || '',
})
